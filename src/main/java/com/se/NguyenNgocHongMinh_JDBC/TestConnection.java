package com.se.NguyenNgocHongMinh_JDBC;

import java.sql.Connection;
import java.sql.DriverManager;

public class TestConnection {
    public static void main(String[] args) {
        try {
            // Tắt GSSAPI (Kerberos)
            System.setProperty("org.mariadb.jdbc.disableGssapi", "true");

            String url = "************************************************************************************************************";
            Class.forName("org.mariadb.jdbc.Driver");
            Connection conn = DriverManager.getConnection(url);

            System.out.println("✅ Kết nối thành công đến MariaDB!");
            conn.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
