package com.se.NguyenNgocHongMinh_JDBC.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .anyRequest().permitAll()  // Cho phép tất cả request không cần authentication
            )
            .csrf(csrf -> csrf.disable())  // Tắt CSRF protection
            .httpBasic(httpBasic -> httpBasic.disable())  // Tắt HTTP Basic Auth
            .formLogin(formLogin -> formLogin.disable())  // Tắt Form Login
            .logout(logout -> logout.disable());  // Tắt Logout

        return http.build();
    }
}
